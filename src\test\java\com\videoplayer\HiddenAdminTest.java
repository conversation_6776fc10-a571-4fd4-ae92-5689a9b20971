package com.videoplayer;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import com.videoplayer.controller.PageController;
import com.videoplayer.service.VideoService;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 隐藏管理界面测试
 * 验证管理界面的隐藏功能是否正常工作
 */
@WebMvcTest(PageController.class)
public class HiddenAdminTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private VideoService videoService;

    /**
     * 测试旧的管理路径是否已经失效
     */
    @Test
    public void testOldAdminPathNotFound() throws Exception {
        mockMvc.perform(get("/admin"))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试旧的添加视频路径是否已经失效
     */
    @Test
    public void testOldAdminAddPathNotFound() throws Exception {
        mockMvc.perform(get("/admin/add"))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试新的隐藏管理路径是否可以访问
     */
    @Test
    public void testNewAdminPathAccessible() throws Exception {
        mockMvc.perform(get("/jyqk-admin"))
                .andExpect(status().isOk());
    }

    /**
     * 测试新的隐藏添加视频路径是否可以访问
     */
    @Test
    public void testNewAdminAddPathAccessible() throws Exception {
        mockMvc.perform(get("/jyqk-admin/add"))
                .andExpect(status().isOk());
    }
}
